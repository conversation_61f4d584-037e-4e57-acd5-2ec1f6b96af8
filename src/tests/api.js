async function testTokenEndpoint() {
  try {
    console.log("Testing token endpoint...");
    const response = await fetch("http://localhost:3000/auth/token");

    if (response.ok) {
      const data = await response.json();
      console.log("✅ Token endpoint works!");
      console.log("Token:", `${data.token.substring(0, 20)}...`);
      return data.token;
    }
    else {
      console.log("❌ Token endpoint failed:", response.status);
      const errorText = await response.text();
      console.log("Error:", errorText);
      return null;
    }
  }
  catch (error) {
    console.log("❌ Error calling token endpoint:", error.message);
    return null;
  }
}

async function testCleanCodeEndpoint(token) {
  if (!token) {
    console.log("⚠️ Skipping clean code test - no token available");
    return;
  }

  try {
    console.log("\nTesting clean code endpoint...");
    const response = await fetch("http://localhost:3000/clean-code", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
      },
      body: JSON.stringify({
        code: "console.log(\"hello world\");",
        imageUrl: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
      }),
    });

    if (response.ok) {
      const data = await response.json();
      console.log("✅ Clean code endpoint works!");
      console.log("Response:", JSON.stringify(data, null, 2));
    }
    else {
      console.log("❌ Clean code endpoint failed:", response.status);
      const errorText = await response.text();
      console.log("Error:", errorText);
    }
  }
  catch (error) {
    console.log("❌ Error calling clean code endpoint:", error.message);
  }
}

async function runTests() {
  console.log("🧪 Starting API tests...\n");

  const token = await testTokenEndpoint();
  await testCleanCodeEndpoint(token);

  console.log("\n🏁 Tests completed!");
}

// Run the tests
runTests();
